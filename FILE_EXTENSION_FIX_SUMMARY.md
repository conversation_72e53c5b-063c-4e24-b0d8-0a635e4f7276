# 文件扩展名修复总结

## 问题描述

在 `analyse_appendix.py` 中，`appendix_info` 字段的 `file_ext` 存储了错误的扩展名（如 `.action`），而不是文件的真实扩展名（如 `.pdf`）。

### 问题根源

1. **URL路径问题**: 政府采购网站的下载URL通常格式为：
   ```
   http://download.ccgp.gov.cn/oss/download?uuid=xxx
   http://example.com/download.action?fileId=123
   ```

2. **错误的优先级逻辑**: 原代码优先使用URL路径中的扩展名，只有当URL无法确定扩展名时才使用文件内容检测结果。

3. **具体表现**: 当URL路径是 `/download.action` 时，`get_file_extension_from_url()` 返回 `.action`，导致 `appendix_info` 中存储错误的扩展名。

## 修复方案

### 1. 调整扩展名检测优先级

**修改位置**: `analyse_appendix.py` 第2897-2927行

**修改前逻辑**:
```python
# 首先从URL获取扩展名
file_ext = get_file_extension_from_url(appendix_url)

# 文件内容检测
file_info = get_file_info_from_content(file_content)
if file_info:
    detected_ext = file_info.get("ext", "")
    # 只有URL无法确定扩展名时才使用检测结果
    if not file_ext:
        file_ext = detected_ext
```

**修改后逻辑**:
```python
# 优先从文件内容检测真实的文件类型
file_info = get_file_info_from_content(file_content)
if file_info:
    detected_ext = file_info.get("ext", "")
    # 优先使用文件内容检测出的扩展名（真实文件类型）
    file_ext = detected_ext
else:
    # 如果文件内容检测失败，尝试从URL获取扩展名作为备选
    url_ext = get_file_extension_from_url(appendix_url)
    if url_ext:
        file_ext = url_ext
```

### 2. 避免本地文件分析覆盖

**修改位置**: `analyse_appendix.py` 第2945-2976行

**问题**: 本地文件分析会重新覆盖已经正确检测出的 `file_ext`

**解决方案**: 只有当之前没有成功检测出文件类型时才进行本地文件分析
```python
# 只有当之前没有成功检测出文件类型时才进行本地文件分析
if not file_ext:
    file_info_local = get_file_info_from_content(file_content)
    if file_info_local:
        file_ext = file_info_local.get("ext", "")
```

## 修复效果

### 修复前
```json
{
    "url": "http://example.com/download.action?id=123",
    "text": "招标文件",
    "file_ext": ".action",  // 错误：来自URL路径
    "file_link_key": "upload_12345"
}
```

### 修复后
```json
{
    "url": "http://example.com/download.action?id=123", 
    "text": "招标文件.pdf",
    "file_ext": ".pdf",  // 正确：来自文件内容检测
    "file_link_key": "upload_12345"
}
```

## 测试验证

运行 `test_file_ext_fix.py` 验证修复效果：

```bash
python test_file_ext_fix.py
```

**测试结果**:
- ✅ URL扩展名提取功能正常
- ✅ 文件内容类型检测功能正常  
- ✅ 优先级逻辑正确：文件内容检测优先于URL扩展名
- ✅ appendix_info结构正确：存储真实的文件扩展名

## 影响范围

1. **appendix_info字段**: 现在存储正确的文件扩展名
2. **文件上传**: 使用正确的MIME类型和扩展名
3. **文件名处理**: 避免错误的扩展名影响文件名生成
4. **向后兼容**: 保持原有API接口不变

## 关键改进

1. **真实性**: `file_ext` 现在反映文件的真实类型，而不是URL路径的假扩展名
2. **可靠性**: 优先使用文件内容检测，提高扩展名识别的准确性
3. **一致性**: 统一了扩展名检测逻辑，避免多处覆盖导致的不一致
4. **健壮性**: 保留了多层兜底机制，确保在各种情况下都能确定文件类型
