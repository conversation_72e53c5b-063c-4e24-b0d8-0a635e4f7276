#!/usr/bin/env python3
"""
测试文件扩展名修复的脚本
验证从文件内容检测的扩展名优先于URL扩展名
"""

import os
import sys
from unittest.mock import patch, MagicMock

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from analyse_appendix import get_file_extension_from_url, get_file_info_from_content


def test_url_extension_extraction():
    """测试URL扩展名提取功能"""
    print("\n" + "=" * 60)
    print("测试1: URL扩展名提取")
    print("=" * 60)
    
    test_cases = [
        ("http://example.com/file.pdf", ".pdf"),
        ("http://example.com/download.action?id=123", ".action"),
        ("http://download.ccgp.gov.cn/oss/download?uuid=177f87be", ""),
        ("http://example.com/file.docx", ".docx"),
        ("http://example.com/path/to/file.zip", ".zip"),
    ]
    
    for url, expected in test_cases:
        result = get_file_extension_from_url(url)
        status = "✅" if result == expected else "❌"
        print(f"{status} URL: {url}")
        print(f"    期望: {expected}, 实际: {result}")


def test_file_content_detection():
    """测试文件内容检测功能"""
    print("\n" + "=" * 60)
    print("测试2: 文件内容类型检测")
    print("=" * 60)
    
    # 模拟不同类型的文件内容
    test_cases = [
        (b"%PDF-1.4", ".pdf", "PDF文件"),
        (b"PK\x03\x04", ".zip", "ZIP文件"),
        (b"\xd0\xcf\x11\xe0", ".doc", "DOC文件"),
        (b"", None, "空文件"),
    ]
    
    for content, expected_ext, description in test_cases:
        result = get_file_info_from_content(content)
        if result:
            actual_ext = result.get("ext", "")
            status = "✅" if actual_ext == expected_ext else "❌"
            print(f"{status} {description}: {actual_ext}")
        else:
            status = "✅" if expected_ext is None else "❌"
            print(f"{status} {description}: 无法检测")


def test_priority_logic():
    """测试优先级逻辑：文件内容检测优先于URL扩展名"""
    print("\n" + "=" * 60)
    print("测试3: 扩展名优先级逻辑")
    print("=" * 60)
    
    # 模拟场景：URL显示.action，但文件内容是PDF
    url = "http://example.com/download.action?id=123"
    pdf_content = b"%PDF-1.4\x0A%\xE2\xE3\xCF\xD3\x0A"  # PDF文件头
    
    # 从URL获取扩展名
    url_ext = get_file_extension_from_url(url)
    print(f"从URL获取的扩展名: {url_ext}")
    
    # 从文件内容检测扩展名
    file_info = get_file_info_from_content(pdf_content)
    content_ext = file_info.get("ext", "") if file_info else ""
    print(f"从文件内容检测的扩展名: {content_ext}")
    
    # 应用新的优先级逻辑
    if file_info and content_ext:
        final_ext = content_ext
        source = "文件内容检测"
    elif url_ext:
        final_ext = url_ext
        source = "URL路径"
    else:
        final_ext = ".bin"
        source = "兜底方案"
    
    print(f"最终使用的扩展名: {final_ext} (来源: {source})")
    
    # 验证结果
    if final_ext == ".pdf":
        print("✅ 优先级逻辑正确：使用了文件内容检测的结果")
    else:
        print("❌ 优先级逻辑错误：应该使用文件内容检测的结果")


def test_appendix_info_structure():
    """测试appendix_info数据结构"""
    print("\n" + "=" * 60)
    print("测试4: appendix_info数据结构")
    print("=" * 60)
    
    # 模拟修复后的appendix_info结构
    appendix_info = {
        "url": "http://example.com/download.action?id=123",
        "text": "招标文件.pdf",  # 使用真实文件名
        "file_ext": ".pdf",  # 使用真实扩展名，不是.action
        "file_link_key": "upload_12345",
    }
    
    print("修复后的appendix_info结构:")
    for key, value in appendix_info.items():
        print(f"  {key}: {value}")
    
    # 验证关键字段
    if appendix_info["file_ext"] == ".pdf":
        print("✅ file_ext字段正确：使用了真实的文件扩展名")
    else:
        print("❌ file_ext字段错误：应该是.pdf而不是.action")


if __name__ == "__main__":
    print("文件扩展名修复测试")
    print("=" * 60)
    
    test_url_extension_extraction()
    test_file_content_detection()
    test_priority_logic()
    test_appendix_info_structure()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    print("\n修复说明:")
    print("1. 优先使用文件内容检测的扩展名（真实文件类型）")
    print("2. 只有当文件内容检测失败时，才使用URL扩展名")
    print("3. appendix_info中的file_ext字段现在存储真实的文件扩展名")
    print("4. 避免了.action等URL路径扩展名覆盖真实文件类型的问题")
