# 黑名单功能使用说明

## 功能概述

黑名单功能用于管理处理失败的文档ID，避免重复处理那些LLM解析三次都失败的文档，从而节省计算资源和时间。

## 核心特性

### 1. 自动黑名单管理
- 当LLM API调用三次都失败时，自动将文档ID添加到黑名单
- 下次运行时自动跳过黑名单中的文档
- 记录失败原因、失败次数、失败时间等详细信息

### 2. 智能过滤
- 在文档选择阶段自动过滤掉黑名单文档
- 与已处理文档列表合并，确保不重复处理
- 启动时显示黑名单统计信息

### 3. 完整的管理工具
- 查看黑名单统计信息
- 列出黑名单文档
- 手动移除特定文档
- 清空整个黑名单

## 使用方法

### 1. 正常运行（自动黑名单功能）

```bash
# 正常运行程序，黑名单功能自动生效
python analyse_appendix.py
```

程序启动时会显示黑名单统计：
```
2025-07-03 10:15:25.893 | INFO | 黑名单中有 5 个文档将被跳过
2025-07-03 10:15:25.893 | INFO | 今日新增黑名单文档: 2 个
```

当LLM调用失败时会自动添加到黑名单：
```
2025-07-03 09:55:50.043 | WARNING | LLM调用三次失败，将文档 -1o4r5cBsUtJ06NfkR31 添加到黑名单
2025-07-03 09:55:50.044 | INFO | ✓ 文档 -1o4r5cBsUtJ06NfkR31 已添加到黑名单，下次运行将跳过
```

### 2. 黑名单管理命令

#### 查看统计信息
```bash
python analyse_appendix.py blacklist --action stats
```

输出示例：
```
============================================================
黑名单统计信息
============================================================
总计黑名单文档: 5
今日新增: 2

最近失败的文档:
  1. -1o4r5cBsUtJ06NfkR31 - 巴楚县中医医院中草药饮片采购项目中标(成交)结果公告
     失败时间: 2025-07-03 02:15:26
  2. -1kMqpcBsUtJ06NfAuOo - 北京大学人民医院诊疗能力提升项目公开招标公告
     失败时间: 2025-07-03 02:15:25
============================================================
```

#### 列出黑名单文档
```bash
# 列出前20个黑名单文档（默认）
python analyse_appendix.py blacklist --action list

# 列出前50个黑名单文档
python analyse_appendix.py blacklist --action list --limit 50
```

输出示例：
```
黑名单文档列表 (显示前5条):
====================================================================================================
1. ID: -1o4r5cBsUtJ06NfkR31
   标题: 巴楚县中医医院中草药饮片采购项目中标(成交)结果公告
   失败次数: 1
   最后失败时间: 2025-07-03 02:15:26
   失败原因: LLM API调用失败: 504 Gateway Time-out...
----------------------------------------------------------------------------------------------------
2. ID: -1kMqpcBsUtJ06NfAuOo
   标题: 北京大学人民医院诊疗能力提升项目公开招标公告
   失败次数: 2
   最后失败时间: 2025-07-03 02:15:25
   失败原因: LLM API调用失败: JSON解析错误...
----------------------------------------------------------------------------------------------------
```

#### 手动添加文档到黑名单
```bash
# 完整参数添加
python analyse_appendix.py blacklist --action add --id 文档ID --title "文档标题" --url "文档URL" --reason "添加原因"

# 最小参数添加（只需要ID）
python analyse_appendix.py blacklist --action add --id 文档ID

# 对于以-开头的文档ID，使用等号格式（重要！）
python analyse_appendix.py blacklist --action add --id="-1kMqpcBsUtJ06NfAuOo" --title "已知有问题的文档" --reason "手动添加到黑名单"

# 普通文档ID示例
python analyse_appendix.py blacklist --action add --id normal_doc_123 --title "普通文档" --reason "测试添加"
```

输出示例：
```
✓ 已将文档 -1kMqpcBsUtJ06NfAuOo 添加到黑名单
  标题: 已知有问题的文档
  URL:
  原因: 手动添加到黑名单
```

**参数说明**：
- `--id`：必需，文档ID
- `--title`：可选，文档标题
- `--url`：可选，文档URL
- `--reason`：可选，添加原因

**重要提示**：
- 对于以`-`开头的文档ID（如`-1kMqpcBsUtJ06NfAuOo`），必须使用等号格式：`--id="-1kMqpcBsUtJ06NfAuOo"`
- 如果使用空格分隔格式会导致参数解析错误
- 普通文档ID可以使用任意格式：`--id normal_doc` 或 `--id=normal_doc`

#### 移除特定文档
```bash
# 从黑名单中移除特定文档ID（以-开头的ID使用等号格式）
python analyse_appendix.py blacklist --action remove --id="-1o4r5cBsUtJ06NfkR31"

# 普通文档ID
python analyse_appendix.py blacklist --action remove --id normal_doc_123
```

输出示例：
```
✓ 已从黑名单中移除文档: -1o4r5cBsUtJ06NfkR31
```

#### 清空黑名单
```bash
python analyse_appendix.py blacklist --action clear
```

输出示例：
```
确认要清空整个黑名单吗？(y/N): y
✓ 黑名单已清空
```

## 数据存储

### 数据库文件
- 文件名：`blacklist.db`（SQLite数据库）
- 位置：程序运行目录
- 自动创建，无需手动配置

### 数据库表结构
```sql
CREATE TABLE blacklist (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    document_id TEXT UNIQUE NOT NULL,           -- 文档ID
    document_title TEXT,                        -- 文档标题
    document_url TEXT,                          -- 文档URL
    failure_reason TEXT,                        -- 失败原因
    failure_count INTEGER DEFAULT 1,           -- 失败次数
    first_failure_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- 首次失败时间
    last_failure_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,   -- 最后失败时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP           -- 创建时间
);
```

## 工作流程

### 1. 程序启动
1. 初始化黑名单管理器
2. 加载黑名单数据库
3. 显示黑名单统计信息

### 2. 文档选择
1. 获取已处理文档ID列表
2. 获取黑名单文档ID列表
3. 合并排除列表
4. 构建ES查询，排除已处理和黑名单文档

### 3. 文档处理
1. 正常处理文档
2. 如果LLM调用失败（已重试3次），自动添加到黑名单
3. 记录失败原因和时间

### 4. 异常处理
1. 检测LLM调用失败错误
2. 提取文档信息（ID、标题、URL）
3. 添加到黑名单数据库
4. 记录日志

## 最佳实践

### 1. 定期维护
- 定期查看黑名单统计：`python analyse_appendix.py blacklist --action stats`
- 检查是否有可以重新处理的文档
- 清理过期的黑名单记录

### 2. 故障排除
- 如果某个文档被误加入黑名单，可以手动移除
- 如果网络问题导致大量文档进入黑名单，可以在问题解决后清空黑名单

### 3. 监控建议
- 关注今日新增黑名单文档数量
- 如果新增数量异常，检查网络或API状态
- 定期备份黑名单数据库

## 常见问题

### Q: 黑名单文档会永久被跳过吗？
A: 是的，除非手动从黑名单中移除。建议定期检查黑名单，确认是否需要重新处理某些文档。

### Q: 如何重新处理黑名单中的文档？
A: 使用移除命令：`python analyse_appendix.py blacklist --action remove --id 文档ID`

### Q: 黑名单数据库文件可以删除吗？
A: 可以，删除后会重新创建空的黑名单数据库。但这会丢失所有黑名单记录。

### Q: 如何备份黑名单数据？
A: 直接复制 `blacklist.db` 文件即可。

### Q: 如何手动添加文档到黑名单？
A: 使用命令：`python analyse_appendix.py blacklist --action add --id 文档ID --title "标题" --reason "原因"`

### Q: 手动添加时哪些参数是必需的？
A: 只有 `--id` 参数是必需的，其他参数（title、url、reason）都是可选的。

### Q: 手动添加的文档和自动添加的有什么区别？
A: 功能上没有区别，都会在下次运行时被跳过。区别只在于添加原因的记录。

### Q: 黑名单功能会影响性能吗？
A: 影响很小。黑名单查询使用了索引优化，对性能影响微乎其微。

## 相关文件

- `blacklist_manager.py`：黑名单管理核心模块
- `analyse_appendix.py`：主程序（集成黑名单功能）
- `test_blacklist.py`：黑名单功能测试
- `blacklist.db`：黑名单数据库文件（自动创建）
- `BLACKLIST_USAGE.md`：本使用说明文档

## 总结

黑名单功能有效解决了重复处理失败文档的问题，通过自动化管理和完整的命令行工具，大大提高了系统的效率和可维护性。建议在生产环境中启用此功能，并定期进行维护。

python -c "
import sqlite3
from collections import Counter

# 连接数据库
conn = sqlite3.connect('blacklist.db')
cursor = conn.cursor()

# 查询所有失败原因
cursor.execute('SELECT failure_reason FROM blacklist WHERE failure_reason IS NOT NULL')
reasons = cursor.fetchall()

if not reasons:
    print('blacklist.db中没有失败原因数据')
else:
    # 统计失败原因类型和数量
    reason_list = [reason[0] for reason in reasons]
    reason_counter = Counter(reason_list)
    
    print(f'blacklist.db失败原因统计:')
    print('=' * 60)
    print(f'总记录数: {len(reason_list)}')
    print(f'失败原因类型数: {len(reason_counter)}')
    print('=' * 60)
    
    # 按数量降序排列
    for reason, count in reason_counter.most_common():
        print(f'{count:4d} 个 - {reason}')

conn.close()
"

python -c "
import sqlite3

# 连接数据库
conn = sqlite3.connect('blacklist.db')
cursor = conn.cursor()

# 查询要删除的记录数量
cursor.execute('SELECT COUNT(*) FROM blacklist WHERE failure_reason = ?', 
               ('公告类型999：标题不包含合同关键词（合同、服务协议）',))
count_before = cursor.fetchone()[0]
print(f'删除前：共有 {count_before} 条记录匹配该失败原因')

# 删除指定失败原因的记录
cursor.execute('DELETE FROM blacklist WHERE failure_reason = ?', 
               ('公告类型999：标题不包含合同关键词（合同、服务协议）',))

# 获取删除的记录数
deleted_count = cursor.rowcount
print(f'已删除 {deleted_count} 条记录')

# 提交更改
conn.commit()

# 查询删除后的总记录数
cursor.execute('SELECT COUNT(*) FROM blacklist')
total_remaining = cursor.fetchone()[0]
print(f'删除后：blacklist.db中剩余 {total_remaining} 条记录')

# 查询剩余的失败原因统计
print('\n剩余失败原因统计:')
print('=' * 50)
cursor.execute('SELECT failure_reason, COUNT(*) FROM blacklist WHERE failure_reason IS NOT NULL GROUP BY failure_reason ORDER BY COUNT(*) DESC')
remaining_reasons = cursor.fetchall()

for reason, count in remaining_reasons:
    print(f'{count:4d} 个 - {reason[:80]}...' if len(reason) > 80 else f'{count:4d} 个 - {reason}')

conn.close()
print('\n✓ 删除操作完成')
"